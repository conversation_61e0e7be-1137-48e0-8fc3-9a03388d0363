# PiePerseus

我不会再维护这个项目了，因为我的AL账户（Little Enterprise上的`4pii4`和`pie guild alt`）已被永久封禁。

在一分钟内测试你的Perseus修改的"开发环境"。

## 功能和目标：
- [x] 无聊天/名称审查
- [x] 使用`json`格式的配置文件（而不是过时的`.ini`）
- [x] 聊天命令
- [x] 移除低士气警告
- [x] 更高的自动重复限制
- [ ] 跳过动画
  - [x] 跳过战舰开火动画
- [ ] 隐藏~~潜在的~~NSFW图像/屏幕
  - [x] 隐藏加载背景图像
- [x] 移除困难模式的属性/阵容要求
- [ ] 指挥官姓名/ID/石油/硬币/钻石伪装
  - [x] 指挥官姓名/ID/等级伪装
- [x] 将任何舰船蓝图转换为原型核心（[未实现的反馈](https://discord.com/channels/456397991847395339/468804193646542849/1241706652261159022)）

我确实会阅读AL的反馈频道，所以如果你有有趣的建议，可以在discord上私信`pi.kt`。

## 文档

* [聊天命令](docs/chat_commands.md)
* [配置](docs/configuration.md)

## 要求：
* 支持f-string的Python
* 路径中有`java`
* 安装了以下组件并在`PATH`中的Android Studio：
  * NDK：用于`ndk-build[.cmd]`
  * Android SDK：用于`zipalign[.exe]`和`apksigner[.bat]`

## 使用方法
```
$ python perseus.py --help
usage: perseus apk builder [-h] [--skip | --no-skip] [--quick-rebuild | --no-quick-rebuild] [--clean | --no-clean]

为你构建apk（如果不带参数调用，这是默认行为）

选项：
  -h, --help            显示此帮助信息并退出
  --skip, --no-skip     如果可能的话跳过反编译和提取
  --quick-rebuild, --no-quick-rebuild
                        通过替换apk中的库来重建apk，而不是使用apktool（节省40秒）
  --clean, --no-clean   删除构建的apk、反编译的源代码、编译的perseus库和xapk
```

第一次构建需要3到5分钟，后续构建需要15-20秒。

如果构建的APK对你不起作用且你在Windows上，请尝试在Git Bash/MSYS中运行脚本。


引用Perseus原创作者的话：

![image](https://image.pieland.xyz/file/672d4d4be9f333cb70c49.png)
