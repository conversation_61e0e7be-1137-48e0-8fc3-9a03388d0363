name: PiePerseus Build

on:
  workflow_dispatch:

permissions:
  contents: write

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Setup Java (JDK 17)
        uses: actions/setup-java@v3
        with:
          java-version: "17"
          distribution: "adopt"

      - name: Setup Android SDK (platform-tools, build-tools, ndk)
        uses: android-actions/setup-android@v3
        with:
          api-level: 33
          components: |
            platform-tools
            build-tools;32.0.0
            ndk;21.4.7075529
            cmdline-tools;latest

      - name: Install system packages (7zip, unzip, jq, wget)
        run: |
          sudo apt-get update
          sudo apt-get install -y p7zip-full unzip jq wget zip netcat-openbsd

      - name: Make repo bin/ executables and add to PATH
        run: |
          # Ensure repo/bin exists and is executable
          if [ -d "$GITHUB_WORKSPACE/bin" ]; then
            chmod +x "$GITHUB_WORKSPACE/bin"/* || true
            echo "$GITHUB_WORKSPACE/bin" >> $GITHUB_PATH
            ls -lah "$GITHUB_WORKSPACE/bin"
          else
            echo "No bin/ directory found in repo root"
          fi

      - name: Ensure Android tools (ndk/build-tools) are in PATH
        run: |
          # Add first found NDK folder to PATH (if present)
          if [ -d "$ANDROID_SDK_ROOT/ndk" ]; then
            ndk_dir=$(ls -1 "$ANDROID_SDK_ROOT/ndk" | head -n1)
            echo "Found NDK: $ndk_dir"
            echo "$ANDROID_SDK_ROOT/ndk/$ndk_dir" >> $GITHUB_PATH
          fi

          # Add build-tools (prefer 32.0.0)
          if [ -d "$ANDROID_SDK_ROOT/build-tools/32.0.0" ]; then
            echo "$ANDROID_SDK_ROOT/build-tools/32.0.0" >> $GITHUB_PATH
          else
            for d in "$ANDROID_SDK_ROOT"/build-tools/*; do
              [ -d "$d" ] && echo "$d" >> $GITHUB_PATH && break
            done
          fi

          # platform-tools
          if [ -d "$ANDROID_SDK_ROOT/platform-tools" ]; then
            echo "$ANDROID_SDK_ROOT/platform-tools" >> $GITHUB_PATH
          fi

      - name: Show environment & verify tools (debug)
        run: |
          echo "WORKSPACE=$GITHUB_WORKSPACE"
          echo "JAVA_HOME=$JAVA_HOME"
          echo "ANDROID_SDK_ROOT=$ANDROID_SDK_ROOT"
          which java || true
          java -version || true
          which python3 || true
          python3 -V || true
          which 7zz || true
          which apkeep || true
          which apktool || true
          which zipalign || true
          which apksigner || true
          ndk-build --version || true
          ls -lah packages || true
          ls -lah bin || true

      - name: Install Python deps (if any) and run pieperseus.py
        run: |
          python3 -m pip install --upgrade pip
          # If your script requires any pip packages you can install them here:
          # pip3 install <package>
          # Run pieperseus.py from repository root (script expects to be run from repo root)
          python3 pieperseus.py --no-skip
        env:
          ANDROID_SDK_ROOT: ${{ env.ANDROID_SDK_ROOT }}
          JAVA_HOME: ${{ env.JAVA_HOME }}

      - name: Set PERSEUS_VERSION env from built apk filename
        run: |
          apkfile=$(ls apk_build/*.patched.apk 2>/dev/null | head -n1 || true)
          if [ -z "$apkfile" ]; then
            echo "No apk found in apk_build/ - skipping PERSEUS_VERSION set"
            echo "PERSEUS_VERSION=unknown" >> $GITHUB_ENV
          else
            version=$(basename "$apkfile" | sed -E 's/.*-([0-9A-Za-z_.]+)\.patched\.apk/\1/')
            echo "Built apk: $apkfile"
            echo "PERSEUS_VERSION=$version" >> $GITHUB_ENV
          fi

      - name: Create Draft Release with APK(s)
        uses: marvinpinto/action-automatic-releases@latest
        with:
          repo_token: "${{ secrets.GITHUB_TOKEN }}"
          automatic_release_tag: "latest"
          draft: true
          title: "Perseus Release v${{ env.PERSEUS_VERSION }}"
          files: |
            apk_build/*.patched.apk
