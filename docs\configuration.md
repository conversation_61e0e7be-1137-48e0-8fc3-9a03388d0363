## Configuration

Note: values that don't exist will be treated as disabled.

```json
{
    "OriginalRepo": "github.com/Egoistically/Perseus",
    "PieRepo": "github.com/4pii4/PiePerseus",

    "Aircraft": {
        "Enabled": false,
        "Accuracy": -1,
        "AccuracyGrowth": -1,
        "AttackPower": -1,
        "AttackPowerGrowth": -1,
        "CrashDamage": -1,
        "Hp": -1,
        "HpGrowth": -1,
        "Speed": -1
    },

    "Enemies": {
        "Enabled": false,
        "AntiAir": -1,
        "AntiAirGrowth": -1,
        "AntiSubmarine": -1,
        "Armor": -1,
        "ArmorGrowth": -1,
        "Cannon": -1,
        "CannonGrowth": -1,
        "Evasion": -1,
        "EvasionGrowth": -1,
        "Hit": -1,
        "HitGrowth": -1,
        "Hp": -1,
        "HpGrowth": -1,
        "Luck": -1,
        "LuckGrowth": -1,
        "Reload": -1,
        "ReloadGrowth": -1,
        "RemoveBuffs": false,
        "RemoveEquipment": false,
        "RemoveSkill": false,
        "Speed": -1,
        "SpeedGrowth": -1,
        "Torpedo": -1,
        "TorpedoGrowth": -1
    },

    "Weapons": {
        "Enabled": false,
        "Damage": -1,
        "ReloadMax": -1
    },
    
    "Spoof": {
        "Enabled": false,
        "Name": "",
        "Id": "",
        "Lv": 0
    },

    "Misc": {
        "Enabled": false,
        "ExerciseGodmode": false,
        "FastStageMovement": false,
        "Skins": false,
        "AutoRepeatLimit": -1,
        "ChatCommands":  false,
        "RemoveBBAnimation": false,
        "RemoveMoraleWarning": false,
        "RemoveHardModeStatLimit": false,
        "RemoveNSFWArts": false,
        "AllBlueprintsConvertible": false
    }
}
```